/**
 * 移动端专用HTTP请求工具
 * 用于处理钉钉登录后的API请求
 */
import axios from 'axios'
import { useUserStore } from '@/store/mobile/user'
import { baseUrl } from '@/config/env'
import { isURL } from '@/utils/validate'
import NProgress from 'nprogress'

// 创建移动端专用的axios实例
const mobileAxios = axios.create({
  timeout: 20000,
  validateStatus: function (status) {
    return status >= 200 && status <= 500
  },
  withCredentials: true
})

// 全局锁机制相关变量
let isRefreshing = false
let refreshTokenPromise = null
let failedQueue = []

// 处理队列中的请求
const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error)
    } else {
      prom.resolve(token)
    }
  })
  
  failedQueue = []
}

// 请求拦截器
mobileAxios.interceptors.request.use(
  async config => {
    NProgress.start()

    // 处理URL
    if (!isURL(config.url) && !config.url.startsWith(baseUrl)) {
      config.url = baseUrl + config.url
    }

    // 基础headers
    config.headers['Blade-Requested-With'] = 'BladeHttpRequest'
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json'
    config.headers['Tenant-Id'] = '000000'

    // 添加移动端token
    const userStore = useUserStore()
    if (userStore.accessToken) {
      // 检查是否是开发模式的模拟token
      const isDevelopment = import.meta.env.DEV
      const isMockToken = userStore.accessToken.startsWith('mock_token_') ||
                         userStore.accessToken.includes('mock_signature_')

      // 在每次请求前检查token有效性
      if (!isDevelopment || !isMockToken) {
        try {
          // 跳过token刷新请求本身，避免循环调用
          if (!config.url.includes('/api/blade-auth/oauth/token')) {
            await userStore.ensureTokenValid()
          }
        } catch (error) {
          console.error('Token验证失败:', error)
          // Token验证失败，跳转登录
          userStore.redirectToLogin()
          NProgress.done()
          return Promise.reject(new Error('Token验证失败，请重新登录'))
        }
      } else {
        console.log('🧪 开发模式：跳过模拟token验证')
      }

      config.headers['Blade-Auth'] = `Bearer ${userStore.accessToken}`
    }

    return config
  },
  error => {
    NProgress.done()
    return Promise.reject(error)
  }
)

// 响应拦截器
mobileAxios.interceptors.response.use(
  res => {
    NProgress.done()
    
    const status = res.data.error_code || res.data.code || res.status
    const message = res.data.msg || res.data.error_description || '系统错误'
    
    // 如果是401错误，处理token刷新
    if (status === 401 && !res.config._retry) {
      res.config._retry = true
      
      const userStore = useUserStore()
      
      // 如果当前已经在刷新token，将请求加入队列
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          res.config.headers['Blade-Auth'] = `Bearer ${token}`
          return mobileAxios(res.config)
        }).catch(err => {
          return Promise.reject(err)
        })
      }
      
      // 开始刷新token
      isRefreshing = true
      
      if (!refreshTokenPromise) {
        refreshTokenPromise = userStore.refreshAccessToken()
          .then(newToken => {
            isRefreshing = false
            processQueue(null, newToken)
            refreshTokenPromise = null
            return newToken
          })
          .catch(err => {
            isRefreshing = false
            processQueue(err, null)
            refreshTokenPromise = null
            
            // 刷新失败，跳转登录
            userStore.redirectToLogin()
            return Promise.reject(err)
          })
      }
      
      return refreshTokenPromise.then(token => {
        res.config.headers['Blade-Auth'] = `Bearer ${token}`
        return mobileAxios(res.config)
      })
    }
    
    // 如果是401且已经重试过，直接跳转登录
    if (status === 401 && res.config._retry) {
      const userStore = useUserStore()
      userStore.redirectToLogin()
      return Promise.reject(new Error('登录已过期，请重新登录'))
    }
    
    // 其他错误处理
    if (status !== 200) {
      // 显示错误信息
      if (window.ElMessage) {
        window.ElMessage({
          message: message,
          type: 'error'
        })
      } else {
        console.error('API错误:', message)
      }
      return Promise.reject(new Error(message))
    }
    
    return res
  },
  error => {
    NProgress.done()
    
    // 网络错误或其他错误
    if (error.response && error.response.status === 401) {
      const userStore = useUserStore()
      userStore.redirectToLogin()
      return Promise.reject(new Error('登录已过期，请重新登录'))
    }
    
    return Promise.reject(error)
  }
)

/**
 * 移动端认证请求方法
 * @param {Object} config - axios请求配置
 * @returns {Promise} 请求结果
 */
export async function mobileRequest(config) {
  const userStore = useUserStore()

  // 检查登录状态
  if (!userStore.accessToken) {
    userStore.redirectToLogin()
    throw new Error('用户未登录')
  }

  // 开发模式下的特殊处理：如果是模拟token，跳过token验证
  const isDevelopment = import.meta.env.DEV
  const isMockToken = userStore.accessToken && (
    userStore.accessToken.startsWith('mock_token_') ||
    userStore.accessToken.includes('mock_signature_')
  )

  if (isDevelopment && isMockToken) {
    console.log('🧪 开发模式：使用模拟JWT token，跳过token验证')
    return mobileAxios(config)
  }

  // 如果是真实token，正常处理
  console.log('🔐 使用真实token进行请求')

  // 确保token有效性（请求拦截器中也会检查，这里是双重保险）
  try {
    if (!config.url.includes('/api/blade-auth/oauth/token')) {
      await userStore.ensureTokenValid()
    }
  } catch (error) {
    console.error('Token验证失败:', error)

    // 如果是开发模式且是模拟token，不跳转登录页
    if (isDevelopment && isMockToken) {
      console.warn('🧪 开发模式：模拟token验证失败，但继续执行请求')
      return mobileAxios(config)
    }

    userStore.redirectToLogin()
    throw new Error('Token验证失败，请重新登录')
  }

  return mobileAxios(config)
}

/**
 * 检查用户登录状态
 * @returns {boolean} 是否已登录
 */
export function checkMobileAuth() {
  const userStore = useUserStore()
  return userStore.isLogin && userStore.accessToken
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
export function getCurrentUser() {
  const userStore = useUserStore()
  return userStore.userInfo
}

export default {
  mobileRequest,
  checkMobileAuth,
  getCurrentUser
}
