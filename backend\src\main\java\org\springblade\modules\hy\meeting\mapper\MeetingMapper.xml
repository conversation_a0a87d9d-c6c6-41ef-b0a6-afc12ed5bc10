<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.meeting.mapper.MeetingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="meetingResultMap" type="org.springblade.modules.hy.meeting.pojo.entity.MeetingEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="emergency_contacts" property="emergencyContacts"/>
        <result column="image_url" property="imageUrl"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectMeetingPage" resultMap="meetingResultMap">
        select * from hy_meeting where is_deleted = 0
    </select>


    <select id="exportMeeting" resultType="org.springblade.modules.hy.meeting.excel.MeetingExcel">
        SELECT * FROM hy_meeting ${ew.customSqlSegment}
    </select>

</mapper>
