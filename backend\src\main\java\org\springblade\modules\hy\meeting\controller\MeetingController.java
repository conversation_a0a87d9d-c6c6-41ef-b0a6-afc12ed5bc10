/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.meeting.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.meeting.pojo.entity.MeetingEntity;
import org.springblade.modules.hy.meeting.pojo.vo.MeetingVO;
import org.springblade.modules.hy.meeting.excel.MeetingExcel;
import org.springblade.modules.hy.meeting.wrapper.MeetingWrapper;
import org.springblade.modules.hy.meeting.service.IMeetingService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 会议表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-meeting/meeting")
@Tag(name = "会议表", description = "会议表接口")
public class MeetingController extends BladeController {

	private final IMeetingService meetingService;

	/**
	 * 会议表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入meeting")
	public R<MeetingVO> detail(MeetingEntity meeting) {
		MeetingEntity detail = meetingService.getOne(Condition.getQueryWrapper(meeting));
		return R.data(MeetingWrapper.build().entityVO(detail));
	}
	/**
	 * 会议表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入meeting")
	public R<IPage<MeetingVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> meeting, Query query) {
		IPage<MeetingEntity> pages = meetingService.page(Condition.getPage(query), Condition.getQueryWrapper(meeting, MeetingEntity.class));
		return R.data(MeetingWrapper.build().pageVO(pages));
	}

	/**
	 * 会议表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入meeting")
	public R<IPage<MeetingVO>> page(MeetingVO meeting, Query query) {
		IPage<MeetingVO> pages = meetingService.selectMeetingPage(Condition.getPage(query), meeting);
		return R.data(pages);
	}

	/**
	 * 会议表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入meeting")
	public R save(@Valid @RequestBody MeetingEntity meeting) {
		return R.status(meetingService.save(meeting));
	}

	/**
	 * 会议表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入meeting")
	public R update(@Valid @RequestBody MeetingEntity meeting) {
		return R.status(meetingService.updateById(meeting));
	}

	/**
	 * 会议表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入meeting")
	public R submit(@Valid @RequestBody MeetingEntity meeting) {
		return R.status(meetingService.saveOrUpdate(meeting));
	}

	/**
	 * 会议表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(meetingService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-meeting")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入meeting")
	public void exportMeeting(@Parameter(hidden = true) @RequestParam Map<String, Object> meeting, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<MeetingEntity> queryWrapper = Condition.getQueryWrapper(meeting, MeetingEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Meeting::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(MeetingEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<MeetingExcel> list = meetingService.exportMeeting(queryWrapper);
		ExcelUtil.export(response, "会议表数据" + DateUtil.time(), "会议表数据表", list, MeetingExcel.class);
	}

}
