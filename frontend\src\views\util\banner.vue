<template>
  <div class="banner-container">
    <img
      class="banner-image"
      :src="bannerUrl"
      alt="首页头图"
    />
  </div>
</template>

<script>
import { getList as getMeetingList } from "@/api/meeting/meeting"; // 你的 meeting 列表接口

export default {
  name: "Banner",
  data() {
    return {
      // 默认背景图（用你项目里真实存在的路径）
      defaultUrl: "",
      bannerUrls: [],   // 从服务端获取的图片地址列表
      isLoading: false,
      hasError: false,
    };
  },
  computed: {
    // 当前展示的 banner（此处简单取第一张）
    bannerUrl() {
      return this.bannerUrls[0] || this.defaultUrl;
    },
  },
  async mounted() {
    await this.fetchBannerUrls();
  },
  methods: {
    async fetchBannerUrls() {
      this.isLoading = true;
      this.hasError = false;
      try {
        // 按你的后端分页接口：current, size, params
        // 如果后端有查询条件（比如 type=banner），可以放在第三个参数里
        const res = await getMeetingList(1, 10, {}); 
        // 兼容 Blade 返回结构：{ data: { success, data: { records: [...] } } }
        const records = res?.data?.data?.records || res?.data?.records || [];
        const urls = records
          .map(r => r.imageUrl)
          .filter(Boolean)
          .map(this.normalizeFileUrl);

        // 去重
        this.bannerUrls = Array.from(new Set(urls));
      } catch (e) {
        console.warn("[Banner] 获取会议图片失败：", e);
        this.hasError = true;
      } finally {
        this.isLoading = false;
      }
    },
    // 规范化后端返回的路径：支持 http(s) 与 / 开头的相对路径
    normalizeFileUrl(url) {
      if (!url) return "";
      if (/^https?:\/\//i.test(url)) return url;          // 绝对地址
      if (url.startsWith("/")) return window.location.origin + url; // 同域静态或网关
      // 其他相对路径（如 files/xxx.jpg），也拼到同域
      return `${window.location.origin}/${url.replace(/^\.?\//, "")}`;
    },
  },
};
</script>

<style scoped>
/* 移动端专用样式 - 基于首页样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.banner-container {
  margin: 0;
  padding: 0;
  line-height: 0;
  width: 100%;
  height: 100%;
  background-color: #000b3f;
}

.banner-image {
  display: block;
  width: 100%;
  height: auto;
  object-fit: contain;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}
</style>
