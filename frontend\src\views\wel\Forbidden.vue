<template>
    <div class="page-container">
      <div class="check-in-container">
        <!-- 页面头部 -->
        <div class="page-header">
          <button class="back-btn" @click="goBack">
            <i class="fas fa-arrow-left"></i>
          </button>
          <h1>权限提示</h1>
          <div class="header-placeholder"></div>
        </div>
  
        <!-- 无权限内容 -->
        <div class="check-in-content">
          <div class="no-permission-card list-item">
            <div class="status-icon denied">
              <i class="fas fa-lock"></i>
            </div>
            <h3>无访问权限</h3>
            <p class="permission-description">
              抱歉，您没有访问该功能的权限<br>
              请联系管理员获取相应权限
            </p>
            
            <div class="additional-info">
              <p>如有疑问，请联系客服支持</p>
              <p class="contact-info"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'NoPermission',
    methods: {
      goBack() {
        // 返回上一页
        window.history.back();
      }
    }
  }
  </script>
  
  <style scoped>
  /* 页面通用样式 - 与签到页面保持一致 */
  .page-container {
    width: 100%;
    min-height: 85vh;
    background-size: cover;
    position: relative;
    z-index: 1;
    margin: 0 auto;
    box-sizing: border-box;
  }
  
  .check-in-container {
    width: 100%;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
  }
  
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    margin-bottom: 20px;
  }
  
  .page-header h1 {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    flex: 1;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .back-btn {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(7, 211, 240, 0.3);
    color: #07D3F0;
    font-size: 20px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
  }
  
  .back-btn:hover {
    background: rgba(7, 211, 240, 0.2);
    transform: translateY(-2px);
  }
  
  .header-placeholder {
    width: 40px;
  }
  
  .check-in-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    margin-top: 50px;
    padding: 0 15px;
  }
  
  /* 统一卡片样式 - 与签到页面保持一致 */
  .list-item {
    background: rgba(255, 255, 255, 0.06);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px 20px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid rgba(231, 76, 60, 0.7); /* 改为红色系边框表示无权限 */
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease forwards;
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 400px;
    text-align: center;
  }
  
  /* 卡片悬停效果 - 与签到页面保持一致 */
  .list-item::before {
    content: "";
    position: absolute;
    top: 0;
    left: -150%;
    width: 300%;
    height: 100%;
    background: linear-gradient(
        120deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: skewX(-20deg);
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .list-item:hover::before {
    opacity: 1;
    animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .list-item:hover {
    transform: translateX(3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.09);
  }
  
  /* 无权限状态样式 */
  .status-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 40px;
  }
  
  .status-icon.denied {
    background: rgba(231, 76, 60, 0.2); /* 红色系背景 */
    color: #e74c3c; /* 红色图标 */
    border: 1px solid rgba(231, 76, 60, 0.3);
  }
  
  .no-permission-card h3 {
    font-size: 22px;
    color: #ffffff;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .permission-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 30px;
    line-height: 1.8;
  }
  
  .additional-info {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px dashed rgba(255, 255, 255, 0.1);
  }
  
  .additional-info p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    margin: 5px 0;
  }
  
  .contact-info {
    color: #07D3F0;
    margin-top: 10px;
    font-size: 14px;
    text-decoration: underline;
  }
  
  /* 动画效果 - 与签到页面保持一致 */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes shimmer {
    0% {
      transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
      transform: translateX(100%) translateY(100%) rotate(45deg);
    }
  }
  </style>
      