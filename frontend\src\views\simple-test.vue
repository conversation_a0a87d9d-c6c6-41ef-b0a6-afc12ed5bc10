<template>
  <div class="simple-test">
    <h1>🧪 简单测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正常工作！</p>
    
    <div class="test-links">
      <h3>测试链接：</h3>
      <ul>
        <li>
        </li>
        <li>
          <router-link to="/dev-tools">🛠️ 开发工具</router-link>
        </li>
        <li>
          <router-link to="/mobile">📲 Mobile页面（会触发登录守卫）</router-link>
        </li>
        <li>
          <router-link to="/dinglogin">🔐 钉钉登录页面</router-link>
        </li>
      </ul>
    </div>
    
    <div class="quick-actions">
      <h3>快速操作：</h3>
      <button @click="showTokenInput" class="btn btn-success">使用真实Token登录</button>
      <button @click="simulateLogin" class="btn btn-info">模拟登录（假token）</button>
      <button @click="simulateLogout" class="btn btn-warning">模拟登出</button>
      <button @click="checkAuth" class="btn btn-info">检查登录状态</button>
      <button @click="diagnoseAuth" class="btn btn-warning">诊断登录问题</button>
      <button @click="testMeetingAPI" class="btn btn-danger">测试Meeting API</button>
      <button @click="runFullTest" class="btn btn-primary">运行完整测试</button>
      <button @click="loadConsoleTools" class="btn btn-secondary">加载控制台工具</button>
    </div>

    <!-- Token输入弹窗 -->
    <div v-if="showTokenDialog" class="token-dialog-overlay" @click="closeTokenDialog">
      <div class="token-dialog" @click.stop>
        <h3>🔑 输入真实Token</h3>
        <p>请输入从后台获取的真实access_token：</p>
        <textarea
          v-model="inputToken"
          placeholder="请粘贴您的access_token..."
          rows="4"
          class="token-input"
        ></textarea>

        <div class="user-info-inputs">
          <h4>用户信息（可选）：</h4>
          <input v-model="inputUserName" placeholder="用户名" class="user-input" />
          <input v-model="inputAccount" placeholder="账号" class="user-input" />
          <input v-model="inputUserId" placeholder="用户ID" class="user-input" />
        </div>

        <div class="dialog-buttons">
          <button @click="loginWithRealToken" class="btn btn-success">确认登录</button>
          <button @click="closeTokenDialog" class="btn btn-secondary">取消</button>
        </div>
      </div>
    </div>

    <div class="current-status">
      <h3>当前状态：</h3>
      <p>登录状态: <span :class="authStatusClass">{{ authStatusText }}</span></p>
      <p>当前路由: {{ $route.path }}</p>
      <p v-if="lastApiResult">最后API调用: <span :class="lastApiResult.success ? 'status-success' : 'status-error'">{{ lastApiResult.message }}</span></p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleTest',
  data() {
    return {
      authStatus: false,
      lastApiResult: null,
      showTokenDialog: false,
      inputToken: '',
      inputUserName: '测试用户',
      inputAccount: 'testuser',
      inputUserId: ''
    }
  },
  computed: {
    authStatusText() {
      return this.authStatus ? '已登录' : '未登录'
    },
    authStatusClass() {
      return this.authStatus ? 'status-success' : 'status-error'
    }
  },
  methods: {
    async simulateLogin() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 使用工具进行模拟登录
        const userInfo = authTool.default.login();

        this.checkAuth();
        alert(`模拟登录成功！\n用户: ${userInfo.user_name}\n账号: ${userInfo.account}`);
      } catch (error) {
        console.error('模拟登录失败:', error);
        alert('模拟登录失败，请检查控制台');
      }
    },

    async simulateLogout() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 使用工具进行登出
        authTool.default.logout();

        this.checkAuth();
        alert('模拟登出成功！');
      } catch (error) {
        console.error('模拟登出失败:', error);
        // 备用方案：直接清除localStorage
        localStorage.removeItem('userInfo');
        this.checkAuth();
        alert('模拟登出成功！');
      }
    },

    async checkAuth() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 使用工具检查认证状态
        const result = authTool.default.check();
        this.authStatus = result.isLoggedIn;

        // 在控制台显示详细信息
        if (result.isLoggedIn && result.userInfo) {
          console.log('当前用户信息:', result.userInfo);
        }
      } catch (error) {
        console.error('检查认证状态失败:', error);
        // 备用方案：使用原来的简单检查
        try {
          const userInfo = localStorage.getItem('userInfo');
          if (userInfo) {
            const parsed = JSON.parse(userInfo);
            this.authStatus = !!(parsed.access_token && parsed.user_id);
          } else {
            this.authStatus = false;
          }
        } catch (e) {
          this.authStatus = false;
        }
      }
    },

    async diagnoseAuth() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 运行诊断
        authTool.default.diagnose();

        alert('诊断完成！请查看控制台输出。');
      } catch (error) {
        console.error('诊断失败:', error);
        alert('诊断失败，请检查控制台');
      }
    },

    async runFullTest() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 运行完整测试
        authTool.default.test();

        alert('完整测试已运行，请查看控制台输出');

        // 更新页面状态
        this.checkAuth();
      } catch (error) {
        console.error('运行测试失败:', error);
        alert('运行测试失败，请检查控制台');
      }
    },

    showTokenInput() {
      this.showTokenDialog = true;
    },

    closeTokenDialog() {
      this.showTokenDialog = false;
      this.inputToken = '';
    },

    async loginWithRealToken() {
      if (!this.inputToken.trim()) {
        alert('请输入access_token');
        return;
      }

      try {
        const timestamp = Date.now();
        const userId = this.inputUserId || `real_user_${Math.random().toString(36).substr(2, 9)}`;

        const realUserInfo = {
          // 使用真实的token
          access_token: this.inputToken.trim(),
          refresh_token: `real_refresh_${timestamp}`, // 如果有真实的refresh_token，也可以一起输入
          user_id: userId,
          user_name: this.inputUserName || '真实用户',
          account: this.inputAccount || 'realuser',

          // 钉钉登录特有字段
          source: 'dingtalk',
          authCode: `real_auth_code_${timestamp}`,
          dingUserId: `real_ding_user_${userId}`,

          // 其他用户信息
          avatar: '',
          email: '<EMAIL>',
          phone: '***********',
          loginTime: new Date().toISOString(),

          // Token过期时间（设置为24小时后，实际以后端token为准）
          expires_in: 86400,
          token_expires_at: timestamp + (86400 * 1000),

          // 确保通过路由守卫检查的关键字段
          tenant_id: '000000',
          role_id: 'real_role',
          dept_id: 'real_dept'
        };

        // 保存到localStorage
        localStorage.setItem('userInfo', JSON.stringify(realUserInfo));

        // 尝试初始化Pinia store
        try {
          const { useUserStore } = await import('@/store/mobile/user');
          const userStore = useUserStore();

          // 手动设置store状态
          userStore.accessToken = realUserInfo.access_token;
          userStore.refreshToken = realUserInfo.refresh_token;
          userStore.isLogin = true;
          userStore.userInfo = realUserInfo;

          console.log('✅ Pinia store已同步');
        } catch (error) {
          console.warn('⚠️ 无法同步Pinia store:', error.message);
        }

        this.closeTokenDialog();
        this.checkAuth();

        console.log('✅ 真实Token登录成功:', realUserInfo);
        alert(`真实Token登录成功！\n用户: ${realUserInfo.user_name}\n账号: ${realUserInfo.account}`);

      } catch (error) {
        console.error('真实Token登录失败:', error);
        alert('真实Token登录失败，请检查控制台');
      }
    },

    async testMeetingAPI() {
      try {
        console.log('🧪 开始测试Meeting API...');

        // 动态导入meeting API
        const { getList } = await import('@/api/meeting/meeting.js');

        // 检查当前登录状态
        const userInfo = localStorage.getItem('userInfo');
        if (!userInfo) {
          throw new Error('请先执行模拟登录');
        }

        console.log('📋 当前用户信息:', JSON.parse(userInfo));

        // 调用API
        console.log('🚀 调用 getList API...');
        const response = await getList(1, 10, {});

        console.log('✅ API调用成功:', response);
        this.lastApiResult = {
          success: true,
          message: 'API调用成功'
        };

        alert('Meeting API测试成功！请查看控制台输出。');

      } catch (error) {
        console.error('❌ Meeting API测试失败:', error);
        this.lastApiResult = {
          success: false,
          message: `API调用失败: ${error.message}`
        };

        // 详细错误分析
        if (error.message.includes('401')) {
          console.log('🔍 401错误分析:');
          console.log('1. 检查token是否存在');
          console.log('2. 检查token格式是否正确');
          console.log('3. 检查Pinia store状态');
          console.log('4. 检查mobileRequest配置');
        }

        alert(`Meeting API测试失败: ${error.message}\n请查看控制台了解详细信息。`);
      }
    },

    async loadConsoleTools() {
      try {
        // 动态导入认证测试工具
        await import('@/utils/mobile-auth-test.js');

        console.log('✅ 认证测试工具已加载到控制台');
        console.log('💡 可用命令:');
        console.log('- mobileAuthTest.login()     // 模拟登录');
        console.log('- mobileAuthTest.logout()    // 模拟登出');
        console.log('- mobileAuthTest.check()     // 检查登录状态');
        console.log('- mobileAuthTest.diagnose()  // 诊断登录问题');
        console.log('- mobileAuthTest.test()      // 运行完整测试');
        console.log('- mobileAuthTest.help()      // 查看帮助');

        alert('认证测试工具已加载到控制台！\n请打开开发者工具查看可用命令。');
      } catch (error) {
        console.error('加载工具失败:', error);
        alert('加载工具失败，请检查控制台');
      }
    }
  },

  mounted() {
    this.checkAuth();
  }
}
</script>

<style scoped>
.simple-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.test-links, .quick-actions, .current-status {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-links ul {
  list-style: none;
  padding: 0;
}

.test-links li {
  margin: 10px 0;
}

.test-links a {
  color: #007bff;
  text-decoration: none;
  font-size: 16px;
}

.test-links a:hover {
  text-decoration: underline;
}

.btn {
  padding: 10px 20px;
  margin: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-block;
}

.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-info { background: #17a2b8; color: white; }
.btn-primary { background: #007bff; color: white; }
.btn-secondary { background: #6c757d; color: white; }

.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn:active {
  transform: translateY(0);
}

.quick-actions {
  text-align: center;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

h3 {
  color: #333;
  margin-bottom: 15px;
}

/* Token输入弹窗样式 */
.token-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.token-dialog {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.token-dialog h3 {
  margin-top: 0;
  color: #333;
  text-align: center;
}

.token-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-family: monospace;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 20px;
  box-sizing: border-box;
}

.token-input:focus {
  border-color: #007bff;
  outline: none;
}

.user-info-inputs {
  margin-bottom: 20px;
}

.user-info-inputs h4 {
  margin: 0 0 10px 0;
  color: #555;
  font-size: 16px;
}

.user-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  box-sizing: border-box;
}

.user-input:focus {
  border-color: #007bff;
  outline: none;
}

.dialog-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.dialog-buttons .btn {
  margin: 0;
}
</style>
